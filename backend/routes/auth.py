from flask import Blueprint, request, jsonify, current_app, g
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt, create_access_token,
    create_refresh_token, set_access_cookies, set_refresh_cookies,
    unset_jwt_cookies
)
from datetime import datetime, timezone, timedelta
from services.auth import AuthService
from services.rbac import RBACService
from utils.error_handlers import api_error_handler, validate_request_data, NotFoundError, ValidationError
from utils.limiter import limiter, AUTH_LIMIT, DEFAULT_LIMIT, get_auth_username, get_user_id
from utils.tenant_middleware import tenant_required, has_permission, has_role, set_tenant_context
from utils.mobile_optimization import optimize_for_mobile, detect_mobile_client, optimize_token_for_mobile

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/auth-check', methods=['GET'])
def auth_check():
    """Simple endpoint to check if auth routes are accessible."""
    client_info = {
        'ip': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', 'Unknown'),
        'headers': dict(request.headers)
    }
    current_app.logger.info(f"Auth check called from: {client_info['ip']} with user agent: {client_info['user_agent']}")

    return jsonify({
        'status': 'success',
        'message': 'Auth routes are accessible',
        'client_info': client_info
    }), 200

@auth_bp.route('/register', methods=['POST'])
@limiter.limit(AUTH_LIMIT, key_func=get_auth_username)
@api_error_handler
def register():
    """Register a new user with multi-tenant support."""
    current_app.logger.info("Register endpoint called")

    data = request.get_json()
    current_app.logger.info(f"Register data: {data}")

    # Validate required fields
    validate_request_data(data, ['username', 'email', 'password'])

    # Register user with enhanced fields
    user, message, status_code = AuthService.register_user(
        username=data['username'],
        email=data['email'],
        password=data['password'],
        first_name=data.get('first_name'),
        last_name=data.get('last_name'),
        phone=data.get('phone'),
        restaurant_name=data.get('restaurant_name'),
        role=data.get('role', 'staff')  # Default to staff role
    )

    current_app.logger.info(f"Registration result: {message}, status: {status_code}")

    if status_code != 201:
        return jsonify({'message': message}), status_code

    # Get the user's primary restaurant
    restaurant = AuthService.get_primary_restaurant(user.id)
    restaurant_id = restaurant.id if restaurant else None

    # Generate access token for the new user with restaurant context
    access_token, _, _, _ = AuthService.login_user(
        email=data['email'],
        password=data['password'],
        restaurant_id=restaurant_id
    )

    # Get user's restaurants
    restaurants = AuthService.get_user_restaurants(user.id)

    # Get user's roles for the primary restaurant
    roles = []
    if restaurant_id:
        roles = RBACService.get_user_roles(user.id, restaurant_id)
        roles = [role.name for role in roles]

    return jsonify({
        'message': message,
        'user': user.to_dict(include_restaurants=True),
        'restaurants': [{'id': r.id, 'name': r.name} for r in restaurants],
        'roles': roles,
        'access_token': access_token
    }), status_code

@auth_bp.route('/login', methods=['POST'])
@limiter.limit(AUTH_LIMIT, key_func=get_auth_username)
@api_error_handler
@optimize_for_mobile
def login():
    """Login a user with multi-tenant support."""
    current_app.logger.info("Login endpoint called")

    data = request.get_json()
    current_app.logger.info(f"Login data: {data}")

    # Validate required fields
    validate_request_data(data, ['email', 'password'])

    # Get restaurant_id if provided
    restaurant_id = data.get('restaurant_id')

    # Detect if client is mobile
    is_mobile = detect_mobile_client()
    current_app.logger.info(f"Mobile client detected: {is_mobile}")

    # Login user with restaurant context
    access_token, user, message, status_code = AuthService.login_user(
        email=data['email'],
        password=data['password'],
        restaurant_id=restaurant_id
    )

    current_app.logger.info(f"Login result: {message}, status: {status_code}")

    if status_code != 200:
        return jsonify({'message': message}), status_code

    # Get user's restaurants
    restaurants = AuthService.get_user_restaurants(user.id)

    # Get JWT claims to extract roles and permissions
    claims = {}
    try:
        # This will only work if the token has been verified
        claims = get_jwt()
    except Exception as e:
        current_app.logger.error(f"Error getting JWT claims: {str(e)}")

    # Extract roles and permissions from claims
    roles = claims.get('roles', [])
    permissions = claims.get('permissions', [])

    # If restaurant_id was not provided but user has only one restaurant,
    # get the restaurant_id from the claims
    if not restaurant_id and len(restaurants) == 1:
        restaurant_id = claims.get('restaurant_id')

    # Create refresh token with the same claims
    additional_claims = {}
    if restaurant_id:
        additional_claims = {
            'restaurant_id': restaurant_id,
            'roles': roles,
            'permissions': permissions
        }

    # For mobile clients, extend token expiration
    if is_mobile:
        # Extend token expiration for mobile clients (24 hours)
        mobile_expiration = datetime.utcnow() + timedelta(hours=24)
        access_token = create_access_token(
            identity=user.id,
            additional_claims=additional_claims,
            expires_delta=timedelta(hours=24)
        )
        refresh_token = create_refresh_token(
            identity=user.id,
            additional_claims=additional_claims,
            expires_delta=timedelta(days=30)  # 30 days for mobile
        )
    else:
        # Use default expiration for desktop clients
        refresh_token = create_refresh_token(
            identity=user.id,
            additional_claims=additional_claims
        )

    # Prepare response data
    response_data = {
        'message': message,
        'user': user.to_dict_with_roles(include_restaurants=True),
        'current_restaurant_id': restaurant_id,
        'access_token': access_token,
        'refresh_token': refresh_token
    }

    # Optimize response for mobile
    if is_mobile:
        # Include only essential data for mobile clients
        response_data = {
            'message': message,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name
            },
            'current_restaurant_id': restaurant_id,
            'roles': roles,
            'access_token': access_token,
            'refresh_token': refresh_token
        }

        # Add minimal restaurant data
        if restaurants:
            response_data['restaurants'] = [{'id': r.id, 'name': r.name} for r in restaurants]
    else:
        # Include full data for desktop clients
        response_data = {
            'message': message,
            'user': user.to_dict_with_roles(include_restaurants=True),
            'restaurants': [{'id': r.id, 'name': r.name} for r in restaurants],
            'current_restaurant_id': restaurant_id,
            'roles': roles,
            'permissions': permissions,
            'access_token': access_token,
            'refresh_token': refresh_token
        }

    # Set JWT in HTTP-only cookie for enhanced security using Flask-JWT-Extended
    response = jsonify(response_data)

    # Use Flask-JWT-Extended's built-in cookie setting functions
    from flask_jwt_extended import set_access_cookies, set_refresh_cookies

    # Set access token cookie
    set_access_cookies(response, access_token)

    # Set refresh token cookie
    set_refresh_cookies(response, refresh_token)

    # Add cache control headers for mobile
    if is_mobile:
        response.headers['Cache-Control'] = 'private, max-age=0, no-cache'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

    return response, status_code

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
@optimize_for_mobile
def get_user_profile():
    """Get the current user's profile with multi-tenant information."""
    user_id = get_jwt_identity()
    user = AuthService.get_user_by_id(user_id)

    if not user:
        raise NotFoundError("User not found")

    # Set tenant context
    tenant_id = set_tenant_context()

    # Get user's restaurants
    restaurants = AuthService.get_user_restaurants(user_id)

    # Get JWT claims to extract roles and permissions
    claims = get_jwt()
    roles = claims.get('roles', [])
    permissions = claims.get('permissions', [])

    # If tenant_id is not set but user has only one restaurant, use that
    if not tenant_id and len(restaurants) == 1:
        tenant_id = restaurants[0].id

    # Detect if client is mobile
    is_mobile = detect_mobile_client()

    # Prepare response based on client type
    if is_mobile:
        # Simplified response for mobile clients
        response_data = {
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name
            },
            'current_restaurant_id': tenant_id,
            'roles': roles
        }

        # Add minimal restaurant data
        if restaurants:
            response_data['restaurants'] = [{'id': r.id, 'name': r.name} for r in restaurants]
    else:
        # Full response for desktop clients
        response_data = {
            'user': user.to_dict_with_roles(include_restaurants=True),
            'restaurants': [{'id': r.id, 'name': r.name} for r in restaurants],
            'current_restaurant_id': tenant_id,
            'roles': roles,
            'permissions': permissions
        }

    response = jsonify(response_data)

    # Add cache control headers for mobile
    if is_mobile:
        response.headers['Cache-Control'] = 'private, max-age=300'  # 5 minutes cache for mobile

    return response, 200

@auth_bp.route('/me', methods=['PUT'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def update_user_profile():
    """Update the current user's profile."""
    user_id = get_jwt_identity()
    data = request.get_json()

    # Ensure at least one field is provided
    if not data:
        raise ValidationError("No data provided for update")

    # Only allow updating certain fields
    allowed_fields = ['username', 'email', 'password', 'first_name', 'last_name', 'phone']
    update_data = {k: v for k, v in data.items() if k in allowed_fields}

    if not update_data:
        raise ValidationError("No valid fields provided for update")

    user, message, status_code = AuthService.update_user(user_id, update_data)

    if status_code != 200:
        return jsonify({'message': message}), status_code

    return jsonify({
        'message': message,
        'user': user.to_dict_with_roles(include_restaurants=True)
    }), status_code

@auth_bp.route('/logout', methods=['POST'])
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def logout():
    """Logout a user by clearing the JWT cookies and session data."""
    current_app.logger.info("Logout endpoint called")

    response = jsonify({'message': 'Successfully logged out'})

    # Use Flask-JWT-Extended helper function to clear cookies
    from flask_jwt_extended import unset_jwt_cookies
    unset_jwt_cookies(response)

    # Also clear any custom cookies we might have set
    response.set_cookie('access_token', '', expires=0, path='/')
    response.set_cookie('refresh_token', '', expires=0, path='/')

    # Add cache control headers to prevent caching
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    current_app.logger.info("User logged out successfully")
    return response, 200

@auth_bp.route('/check', methods=['GET'])
@limiter.limit(DEFAULT_LIMIT)
@api_error_handler
def check_auth():
    """Check if the user is authenticated without requiring a valid token."""
    current_app.logger.info("Auth check endpoint called")

    try:
        # Try to verify JWT without raising an exception
        verify_jwt_in_request(optional=True)
        user_id = get_jwt_identity()

        if user_id:
            # User is authenticated, get user data
            user = AuthService.get_user_by_id(user_id)
            if user and user.is_active:
                # Get JWT claims
                claims = get_jwt()
                restaurant_id = claims.get('restaurant_id')

                # Get user's restaurants
                restaurants = AuthService.get_user_restaurants(user_id)

                return jsonify({
                    'authenticated': True,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name
                    },
                    'current_restaurant_id': restaurant_id,
                    'restaurants': [{'id': r.id, 'name': r.name} for r in restaurants],
                    'roles': claims.get('roles', []),
                    'permissions': claims.get('permissions', [])
                }), 200
            else:
                # User not found or inactive
                return jsonify({'authenticated': False, 'reason': 'User not found or inactive'}), 401
        else:
            # No valid token
            return jsonify({'authenticated': False, 'reason': 'No valid token'}), 401

    except Exception as e:
        current_app.logger.debug(f"Auth check failed: {str(e)}")
        return jsonify({'authenticated': False, 'reason': 'Token verification failed'}), 401

@auth_bp.route('/restaurants', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_user_restaurants():
    """Get all restaurants a user has access to."""
    user_id = get_jwt_identity()
    restaurants = AuthService.get_user_restaurants(user_id)

    return jsonify({
        'restaurants': [{'id': r.id, 'name': r.name} for r in restaurants]
    }), 200

@auth_bp.route('/switch-restaurant', methods=['POST'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def switch_restaurant():
    """Switch the user's active restaurant and generate a new token."""
    user_id = get_jwt_identity()
    data = request.get_json()

    # Validate required fields
    validate_request_data(data, ['restaurant_id'])
    restaurant_id = data['restaurant_id']

    # Get user
    user = AuthService.get_user_by_id(user_id)
    if not user:
        raise NotFoundError("User not found")

    # Check if user has access to this restaurant
    restaurants = AuthService.get_user_restaurants(user_id)
    restaurant_ids = [r.id for r in restaurants]

    if restaurant_id not in restaurant_ids:
        return jsonify({
            'message': 'You do not have access to this restaurant'
        }), 403

    # Generate new token with restaurant context
    access_token, _, _, _ = AuthService.login_user(
        email=user.email,
        password=None,  # Not needed since we're using user_id
        restaurant_id=restaurant_id
    )

    # Get user's roles and permissions for this restaurant
    roles = RBACService.get_user_roles(user_id, restaurant_id)
    permissions = RBACService.get_user_permissions(user_id, restaurant_id)

    # Set JWT in HTTP-only cookie
    response = jsonify({
        'message': 'Restaurant switched successfully',
        'restaurant_id': restaurant_id,
        'roles': [role.name for role in roles],
        'permissions': [perm.name for perm in permissions],
        'access_token': access_token
    })

    # Use secure cookies in production with better compatibility
    response.set_cookie(
        'access_token',
        access_token,
        httponly=True,
        secure=current_app.config.get('JWT_COOKIE_SECURE', False),
        max_age=current_app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 3600),
        samesite='Lax',  # Changed from Strict to Lax for better multi-user support
        path='/'
    )

    return response, 200

@auth_bp.route('/users', methods=['GET'])
@jwt_required()
@tenant_required
@has_permission('user:read')
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_restaurant_users():
    """Get all users for the current restaurant."""
    tenant_id = g.tenant_id

    # Query users for this restaurant
    from models.auth_models import user_restaurants
    from models.models import db

    users = db.session.query(User).join(
        user_restaurants, User.id == user_restaurants.c.user_id
    ).filter(
        user_restaurants.c.restaurant_id == tenant_id
    ).all()

    # Get roles for each user
    user_data = []
    for user in users:
        roles = RBACService.get_user_roles(user.id, tenant_id)
        user_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_active': user.is_active,
            'roles': [role.name for role in roles]
        })

    return jsonify({
        'users': user_data
    }), 200

@auth_bp.route('/users/<user_id>/roles', methods=['POST'])
@jwt_required()
@tenant_required
@has_permission('user:update')
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def assign_role_to_user(user_id):
    """Assign a role to a user for the current restaurant."""
    tenant_id = g.tenant_id
    data = request.get_json()

    # Validate required fields
    validate_request_data(data, ['role'])
    role_name = data['role']

    # Assign role to user
    success, message = RBACService.assign_role_to_user(user_id, role_name, tenant_id)

    if not success:
        return jsonify({'message': message}), 400

    return jsonify({'message': message}), 200

@auth_bp.route('/users/<user_id>/roles/<role_name>', methods=['DELETE'])
@jwt_required()
@tenant_required
@has_permission('user:update')
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def remove_role_from_user(user_id, role_name):
    """Remove a role from a user for the current restaurant."""
    tenant_id = g.tenant_id

    # Remove role from user
    success, message = RBACService.remove_role_from_user(user_id, role_name, tenant_id)

    if not success:
        return jsonify({'message': message}), 400

    return jsonify({'message': message}), 200

@auth_bp.route('/roles', methods=['GET'])
@jwt_required()
@tenant_required
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_roles():
    """Get all available roles."""
    from models.auth_models import Role

    roles = Role.query.all()

    return jsonify({
        'roles': [role.to_dict(include_permissions=True) for role in roles]
    }), 200

@auth_bp.route('/permissions', methods=['GET'])
@jwt_required()
@tenant_required
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_permissions():
    """Get all available permissions."""
    from models.auth_models import Permission

    permissions = Permission.query.all()

    return jsonify({
        'permissions': [perm.to_dict() for perm in permissions]
    }), 200

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
@limiter.limit(AUTH_LIMIT, key_func=get_user_id)
@api_error_handler
@optimize_for_mobile
def refresh_token():
    """Refresh the access token using a refresh token."""
    current_app.logger.info("Token refresh endpoint called")

    # Get the user identity from the refresh token
    user_id = get_jwt_identity()

    # Get the JWT claims from the refresh token
    refresh_claims = get_jwt()

    # Get the restaurant_id from the refresh token claims
    restaurant_id = refresh_claims.get('restaurant_id')

    # Detect if client is mobile
    is_mobile = detect_mobile_client()
    current_app.logger.info(f"Mobile client detected: {is_mobile}")

    # Prepare additional claims
    additional_claims = {
        'restaurant_id': restaurant_id,
        'roles': refresh_claims.get('roles', []),
        'permissions': refresh_claims.get('permissions', [])
    }

    # Create a new access token with the same claims
    if is_mobile:
        # Extend token expiration for mobile clients (24 hours)
        access_token = create_access_token(
            identity=user_id,
            additional_claims=additional_claims,
            expires_delta=timedelta(hours=24)
        )
    else:
        # Use default expiration for desktop clients
        access_token = create_access_token(
            identity=user_id,
            additional_claims=additional_claims
        )

    # Update the user's last login timestamp
    user = AuthService.get_user_by_id(user_id)
    if user:
        user.last_login = datetime.now(timezone.utc)
        from models.models import db
        db.session.commit()

    # Return the new access token
    response = jsonify({
        'message': 'Token refreshed successfully',
        'access_token': access_token
    })

    # Set cookie expiration based on client type
    access_token_max_age = 86400 if is_mobile else 3600  # 24 hours for mobile, 1 hour for desktop

    # Set the new access token in a cookie with better compatibility
    response.set_cookie(
        'access_token',
        access_token,
        httponly=True,
        secure=current_app.config.get('JWT_COOKIE_SECURE', False),
        max_age=access_token_max_age,
        samesite='Lax',  # Changed from Strict to Lax for better multi-user support
        path='/'
    )

    # Add cache control headers for mobile
    if is_mobile:
        response.headers['Cache-Control'] = 'private, max-age=0, no-cache'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

    return response, 200
