import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import PropTypes from 'prop-types';

// Icons
import {
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  ArrowPathRoundedSquareIcon,
} from '@heroicons/react/24/outline';

const Header = ({ toggleSidebar, title }) => {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isNotificationMenuOpen, setIsNotificationMenuOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const userMenuRef = useRef(null);
  const notificationMenuRef = useRef(null);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setIsUserMenuOpen(false);
      }
      if (notificationMenuRef.current && !notificationMenuRef.current.contains(event.target)) {
        setIsNotificationMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sample notifications - in a real app, these would come from an API or websocket
  useEffect(() => {
    setNotifications([
      {
        id: 1,
        message: 'New invoice added',
        time: '5 minutes ago',
        read: false,
      },
      {
        id: 2,
        message: 'Price update detected',
        time: '1 hour ago',
        read: false,
      },
      {
        id: 3,
        message: 'System maintenance scheduled',
        time: '1 day ago',
        read: true,
      },
    ]);
  }, []);

  // Handle logout
  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  // Handle switch user (logout without redirect, then go to login)
  const handleSwitchUser = async () => {
    setIsUserMenuOpen(false);
    await logout(false); // Don't redirect automatically
    navigate('/login?switch=true');
  };

  // Navigate to profile
  const goToProfile = () => {
    navigate('/settings');
    setIsUserMenuOpen(false);
  };

  // Mark notification as read
  const markAsRead = (id) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // Count unread notifications
  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center">
          {/* Mobile menu button */}
          <button
            className="p-1 mr-3 text-gray-600 rounded-md lg:hidden hover:bg-gray-100 focus:outline-none"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            <Bars3Icon className="w-6 h-6" />
          </button>

          {/* Page title */}
          <h1 className="text-xl font-semibold text-gray-800">{title}</h1>
        </div>

        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <div className="relative" ref={notificationMenuRef}>
            <button
              className="p-1 text-gray-600 rounded-md hover:bg-gray-100 focus:outline-none"
              onClick={() => setIsNotificationMenuOpen(!isNotificationMenuOpen)}
              aria-label="Notifications"
            >
              <div className="relative">
                <BellIcon className="w-6 h-6" />
                {unreadCount > 0 && (
                  <div className="absolute top-0 right-0 w-4 h-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center transform translate-x-1 -translate-y-1">
                    {unreadCount}
                  </div>
                )}
              </div>
            </button>

            {/* Notification dropdown */}
            {isNotificationMenuOpen && (
              <div className="absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                <div className="py-2 px-4 border-b border-gray-100">
                  <h3 className="text-sm font-medium text-gray-700">Notifications</h3>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`py-3 px-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer ${
                          notification.read ? 'bg-white' : 'bg-blue-50'
                        }`}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-800">
                            {notification.message}
                          </span>
                          {!notification.read && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                              New
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                      </div>
                    ))
                  ) : (
                    <div className="py-4 px-4 text-sm text-gray-500 text-center">
                      No notifications
                    </div>
                  )}
                </div>
                {notifications.length > 0 && (
                  <div className="py-2 px-4 border-t border-gray-100 text-center">
                    <button className="text-sm text-blue-600 hover:text-blue-800">
                      Mark all as read
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* User menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              className="flex items-center text-sm rounded-full focus:outline-none"
              onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
              aria-label="User menu"
            >
              <UserCircleIcon className="w-8 h-8 text-gray-600" />
            </button>

            {isUserMenuOpen && (
              <div className="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                <div className="py-1">
                  {currentUser && (
                    <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                      <div className="font-medium truncate">{currentUser.username}</div>
                      <div className="text-gray-500 truncate">{currentUser.email}</div>
                    </div>
                  )}

                  <button
                    onClick={goToProfile}
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <CogIcon className="mr-3 h-4 w-4 text-gray-500" aria-hidden="true" />
                    Profile & Settings
                  </button>

                  <button
                    onClick={handleSwitchUser}
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <ArrowPathRoundedSquareIcon
                      className="mr-3 h-4 w-4 text-gray-500"
                      aria-hidden="true"
                    />
                    Switch User
                  </button>

                  <button
                    onClick={handleLogout}
                    className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    <ArrowRightOnRectangleIcon
                      className="mr-3 h-4 w-4 text-red-500"
                      aria-hidden="true"
                    />
                    Sign out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

Header.propTypes = {
  toggleSidebar: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
};

export default Header;