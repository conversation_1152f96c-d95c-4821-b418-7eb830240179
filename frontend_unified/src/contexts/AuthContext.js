import React, { createContext, useState, useEffect, useContext } from 'react';
import { authService } from '../services/api';
import api from '../services/api';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

// Get API base URL from the api service
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api/v1';

// Create the context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    const checkLoggedIn = async () => {
      try {
        setLoading(true);
        console.log('AuthContext: Checking authentication status on mount');

        // Use the new auth check endpoint that doesn't require a valid token
        const response = await authService.checkAuth();

        if (response.data && response.data.authenticated) {
          console.log('AuthContext: User is authenticated, setting current user');
          setCurrentUser(response.data.user);
        } else {
          console.log('AuthContext: User is not authenticated:', response.data?.reason);
          setCurrentUser(null);
        }
      } catch (err) {
        console.log('AuthContext: Error checking authentication:', err);
        setCurrentUser(null);
        // Don't set error for authentication check failures
      } finally {
        setLoading(false);
      }
    };

    checkLoggedIn();
  }, []);

  // Login function
  const login = async (credentials) => {
    setLoading(true);
    setError(null);

    try {
      console.log('AuthContext: login attempt started for email:', credentials.email);
      console.log('AuthContext: API URL being used:', API_BASE_URL);

      console.log('AuthContext: calling authService.login...');
      const response = await authService.login(credentials);

      console.log('AuthContext: login response received:', response.status);

      if (response.data && response.data.user) {
        console.log('AuthContext: User data received, setting current user');
        setCurrentUser(response.data.user);
        // Don't navigate here - let the Login component handle navigation
        return true;
      } else {
        console.log('AuthContext: No user data in response');
        return false;
      }
    } catch (err) {
      console.log('AuthContext: Login error details:', err);
      setError(err.message || 'Login failed');

      // Don't show toast here - let the Login component handle error display
      return {
        success: false,
        error: err.message || 'Invalid credentials',
        statusCode: err.response?.status || 500
      };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      console.log('AuthContext: registration attempt started for email:', userData.email);
      const response = await authService.register(userData);

      if (response.data && response.data.user) {
        console.log('AuthContext: User data received, setting current user');
        setCurrentUser(response.data.user);
        // Don't navigate here - let the useEffect in the component handle navigation
        return true;
      } else {
        console.log('AuthContext: No user data in response');
        return false;
      }
    } catch (err) {
      console.log('AuthContext: Registration error details:', err);
      setError(err.message || 'Registration failed');

      // Don't show toast here - let the Register component handle error display
      return {
        success: false,
        error: err.message || 'Registration failed',
        statusCode: err.response?.status || 500
      };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async (redirectToLogin = true) => {
    setLoading(true);
    console.log('AuthContext: Logging out user');

    try {
      await authService.logout();
      console.log('AuthContext: Logout API call successful');
    } catch (err) {
      console.log('AuthContext: Logout API call failed, but continuing:', err);
      // Continue with logout even if API fails
    }

    // Always clear user state
    setCurrentUser(null);
    setError(null);

    // Clear any cached data
    localStorage.removeItem('lastAuthCheck');

    console.log('AuthContext: User state cleared');

    if (redirectToLogin) {
      navigate('/login');
    }

    setLoading(false);
  };

  // Update user profile
  const updateProfile = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.updateProfile(userData);

      if (response.data && response.data.user) {
        setCurrentUser(response.data.user);
        toast.success('Profile updated successfully');
        return true;
      }

      return false;
    } catch (err) {
      setError(err.message || 'Failed to update profile');
      toast.error(err.message || 'Failed to update profile');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Value object to be provided to consumers
  const value = {
    currentUser,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;